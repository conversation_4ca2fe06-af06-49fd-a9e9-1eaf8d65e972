Stack trace:
Frame         Function      Args
0007FFFFAA90  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9990) msys-2.0.dll+0x1FEBA
0007FFFFAA90  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAD68) msys-2.0.dll+0x67F9
0007FFFFAA90  000210046832 (000210285FF9, 0007FFFFA948, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAA90  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAA90  0002100690B4 (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAD70  00021006A49D (0007FFFFAAA0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC265E0000 ntdll.dll
7FFC261A0000 KERNEL32.DLL
7FFC23D00000 KERNELBASE.dll
000210040000 msys-2.0.dll
7FFC24560000 USER32.dll
7FFC23A00000 win32u.dll
7FFC26570000 GDI32.dll
7FFC240F0000 gdi32full.dll
7FFC238B0000 msvcp_win.dll
7FFC24230000 ucrtbase.dll
7FFC25FB0000 advapi32.dll
7FFC24D20000 msvcrt.dll
7FFC255B0000 sechost.dll
7FFC262E0000 RPCRT4.dll
7FFC22E50000 CRYPTBASE.DLL
7FFC23960000 bcryptPrimitives.dll
7FFC25C90000 IMM32.DLL
