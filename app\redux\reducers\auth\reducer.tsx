import types from './actions';

const initialState = {
  userData: {},
  accessToken: '',
  showIntro: true,
  selectedHomeTab: '',
  internetConnected: true,
  darkmode: false,
  selectPosition: '',
  userProfileData: {
    firstName: '',
    lastName: '',
    phoneNumber: '',
    company: '',
    birthDate: '',
    gender: '',
    email: '',
    location: '',
    cv: '',
    certificateFile: '',
    licenses: [],
    countryCode: '',
    profilePhoto: '',
    userRole: '',
    selectedTags: [],
    workExperince: [],
    certifications: [],
  },
  isWalkthroughVisible: false,
  rewardModal: {
    visible: false,
    data: {},
  },
  searchScreen: false,
};

export default function reducer(state = initialState, action: any) {
  switch (action.type) {
  case types.SET_USER_DATA:
    return {
      ...state,
      userData: action.userData,
    };
  case types.SET_USER_PROFILE_DATA:
    return {
      ...state,
      userProfileData: action.userProfileData,
    };
  case types.SET_ACCESS_TOKEN:
    return {
      ...state,
      accessToken: action.accessToken,
    };
  case types.SET_SHOW_INTRO:
    return {
      ...state,
      showIntro: action.showIntro,
    };
  case types.SET_SELECT_POSITION:
    return {
      ...state,
      selectPosition: action.selectPosition,
    };
  case types.SET_SELECTED_HOME_TAB:
    return {
      ...state,
      selectedHomeTab: action.selectedHomeTab,
    };
  case types.SET_INTERNET_CONNECTED:
    return {
      ...state,
      internetConnected: action.internetConnected,
    };
  case types.SET_DARKMODE:
    return {
      ...state,
      darkmode: action.darkmode,
    };
  case types.SET_WALKTHROUGH_VISIBLE:
    return {
      ...state,
      isWalkthroughVisible: action.isVisible,
    };
  case types.SET_REWARD_MODAL:
    return {
      ...state,
      rewardModal: action.rewardModal,
    };
  case types.SET_SEARCH_SCREEN_VISIBLE:
    return {
      ...state,
      searchScreen: action.searchScreen,
    };
  case types.CLEAR_DATA:
    return {
      ...state,
      accessToken: '',
      userData: {},
    };

  default:
    return state;
  }
}
