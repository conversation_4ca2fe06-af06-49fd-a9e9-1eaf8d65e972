import React, { useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import styles from './styles';
import Header from '../../components/Header';
import Upload from '../../components/Upload';
import MyDetails from '../../components/MyDetail';
import About from '../../components/About';
import authActions from '@redux/reducers/auth/actions';
import { isArray, isEmpty } from '@app/utils/lodashFactions';
import { translate } from '@language/Translate';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import {
  formatDate,
  getCountryFlagCode,
  isIOS,
  resetStackScreen,
  updateReview,
  updateUserData,
} from '@app/utils/CommonFunction';
import ReviewComponant from '@components/ReviewComponant';
import { BaseStyles } from '@config/theme';
import { useFocusEffect } from '@react-navigation/native';
import { AiDescriptionType } from '@screens/JobPosting';

// Define the ErrorState type
interface ErrorState {
  err: boolean;
  txt: string;
}

interface NewObj {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  birthDate?: any;
  company?: string;
  gender?: string;
  location?: string;
  countryCode?: string;
  skillIds?: Array<number | string>; // Adjust based on the actual type of `selectedTags`
  about?: string; // Optional because it uses `data?.about`
  isSkipped?: boolean; // Optional because it’s added conditionally
  cv?: any; // Optional because it depends on `cvFile`
  profilePhoto?: any;
  licenses?: string | Array<any>; // Adjust the type of `licenseFiles` accordingly
  isProfileSet?: boolean;
  coordinates?: any;
  flagCode?: string;
  invitedReferralCode?: string;
  isAvailable?: any;
  description?: any;
}

// Type definitions
interface Qualification {
  company: string;
  description?: string | null;
  designation?: string | null;
  docTitle?: string | null;
  docType: string;
  endDate: string | null;
  fileName?: string | null;
  isCurrCompany?: number | null;
  startDate: string | null;
}

export default function ProfileSetUp(props: any) {
  const IOS = Platform.OS === 'ios';
  const {
    navigation,
    route,
    type,
    redirection,
    skipTrue,
  }: {navigation: any; route: any; type?: any; redirection?: any} = props;
  const params = route?.params;
  const isOwnProfileScreen =
    params?.type === 'OWNProfile' || type === 'OWNProfile';
  const orderType = route?.params?.orderType;
  const [description, setDescription] = useState('');
  const previousScreen = route?.params?.previousScreen || '';
  const currentStep = route?.params?.currentStep || 0;
  const [uploadLicenseFile, setUploadLicenseFile] = useState([]);
  const [uploadLicense, setUploadLicene] = useState([]);
  const { selectPosition, userData } = useSelector((state: any) => state.auth); // Use your RootState type
  const { userProfileData } = useSelector((state: any) => state.auth); // Use your RootState type
  const [selectedTags, setSelectedTags] = useState<any>([]);
  const dispatch = useDispatch();
  const [selectedtagErr, setSelectedTagErr] = useState<ErrorState>({
    err: false,
    txt: '',
  });
  const [activeStep, setActiveStep] = useState<number>(
    isOwnProfileScreen ? 4 : selectPosition === 'employer' ? 2 : 1, // Start at 'My Details' if employer
  );
  const [refreshing, setRefreshing] = useState(false);

  const [isUploadValid, setIsUploadValid] = useState(false);
  const [licenseError, setLicenseError] = useState<string | null>(null);
  const [cvError, setCvError] = useState<string | null>(null);
  const [cvFile, setCvFile] = useState<any>(null);
  // const [about, setAbout] = useState<any>('');
  const [licenseFiles, setLicenseFiles] = useState<any>([]);
  const [selectedGender, setSelectedGender] = useState<any>('');
  const [name, setName] = useState<string>('');
  const [firstNameErr, setFirstNameErr] = useState<boolean>(false);
  const [firstNameErrTxt, setFirstNameErrTxt] = useState<string>(
    'First Name is required',
  );
  const [lastNameErr, setLastNameErr] = useState<boolean>(false);
  const [lastNameErrTxt, setLastNameErrTxt] = useState<string>(
    'Last Name is required',
  );
  const [lastname, setLastName] = useState<string>('');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [phoneNumberError, setPhoneNumberError] = useState<boolean>(false);
  const [phoneNumberErrorTxt, setPhoneNumberErrorTxt] = useState<string>(
    'Phone number is required',
  );
  const [companyNameForCerti, setCompanyNameForCerti] = useState<string>('');
  const [companyNameErr, setCompanyNameErr] = useState<boolean>(false);
  const [companyNameErrTxt, setCompanyNameErrTxt] = useState<string>(
    'Company Name is required',
  );
  const [email, setEmail] = useState<string>('');
  const [emailErr, setEmailErr] = useState<boolean>(false);
  const [genderErr, setGenderError] = useState<boolean>(false);
  const [emailErrTxt, setEmailErrTxt] = useState<string>('Email is required');
  const [genderErrTxt, setGenderErrTxt] =
    useState<string>('Gender is required');
  const [location, setLocation] = useState<any>({ description: '' });
  const [locationErr, setLocationErr] = useState<boolean>(false);
  const [locationErrTxt, setLocationErrTxt] = useState<string>(
    'Location is required',
  );
  const [referralCode, setReferralCode] = useState<any>('');
  const [selectDateErr, setSelectDateErr] = useState<boolean>(false);
  const [selectDateErrTxt, setSelectDateErrTxt] =
    useState<string>('Date is required');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [countryCode, setCountryCode] = useState<string>(''); // Initial country code as a string
  const [flagCode, setFlagCode] = useState<string>(''); // Initial country code as a string

  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [imageName, setImageName] = useState<any>('');
  const [loader, setLoader] = useState<any>(false);
  const [isAvailable, setIsAvailable] = useState<any>(false);
  // for working Experince

  const [company, setCompany] = useState<any>('');
  const [companyErr, setCompanyErr] = useState<any>({
    err: false,
    txt: '',
  });
  const [designation, setDesignation] = useState<any>('');
  const [working, setWorking] = useState<any>('');
  const [workingErr, setWorkingErr] = useState<any>({
    err: false,
    txt: '',
  });
  const [designationErr, setDesignationErr] = useState<any>({
    err: false,
    txt: '',
  });

  const [isCurrentCompany, setIsCurrentCompany] = useState<any>('');
  const [jobProfile, setJobProfile] = useState<any>('');
  const [jobProfileErr, setJobProfileErr] = useState<any>({
    err: false,
    txt: '',
  });
  const [workingSince, setWorkingSince] = useState<Date | undefined>(undefined);
  const [workingTill, setWorkingTill] = useState<Date | undefined>(undefined);
  const [workingSinceErr, setWorkingSinceErr] = useState<any>({
    err: false,
    txt: '',
  });
  const [workingTillErr, setWorkingTillErr] = useState<any>({
    err: false,
    txt: '',
  });
  const [workExperince, setWorkExperince] = useState<any>(
    userData?.workExperience ? userData?.workExperience : [],
  );
  const [addData, setAddData] = useState<string | null>(null);

  const [errorShow, setErrorShow] = useState<any>(false);
  const [certificateerrorShow, setCertificateErrorShow] = useState<any>(false);
  const [preCertificate, setPreCertificate] = useState([]);
  const [experienceList, setExperienceList] = useState([
    {
      id: Date.now(),
      company: '',
      docTitle: '',
      designation: '',
      isCurrCompany: 0,
      startDate: undefined,
      endDate: undefined,
      description: '',
      docType: 'workExperience',
    },
  ]);
  const [certificationList, setCertificationList] = useState([
    {
      id: Date.now(),
      company: '',
      startDate: undefined,
      endDate: undefined,
      fileName: '',
      docType: 'certifications',
    },
  ]);

  const [certificateFileError, setcertificateFileError] = useState<
    string | null
  >(null);

  const [companyName, setCompanyName] = useState<any>('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [certificateFile, setCertificateFile] = useState<any>(null);
  const [preExperince, setPreExperince] = useState([]);
  const [aiDescription, setAiDescription] = useState<AiDescriptionType>({
    description: '',
    jobDescription: '',
    showDescription: '',
  });
  const [errors, setErrors] = useState({});
  const [formatedAddress, setFormatedAddresss] = useState(
    userData?.shortAddress || '',
  );

  const ActionSheetRef = useRef<any>();
  const ActionSheetRefIOS = useRef<any>();

  const validateEmailFormat = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const goToStep = async (stepNumber: number) => {
    let isValid = true;
    // Validate Upload Step (Step 1)
    if (stepNumber === 2) {
      // Moving to Step 3 from Step 2 (About)
      let isValidstep = true;

      if (!name) {
        setFirstNameErr(true);
        isValidstep = false;
      } else {
        setFirstNameErr(false);
      }

      if (!lastname) {
        setLastNameErr(true);
        isValidstep = false;
      } else {
        setLastNameErr(false);
      }

      // if (isEmpty(companyName)) {
      //   isValid = false;
      //   setCompanyNameErr(true);
      //   setCompanyNameErrTxt(translate('companyNameRequired', ''));
      // } else if (companyName.length < 3) {
      //   isValid = false;
      //   setCompanyNameErr(true);
      //   setCompanyNameErrTxt(translate('leastCharacters', ''));
      // } else if (!/^[A-Za-z\s]+$/.test(companyName)) {
      //   isValid = false;
      //   setCompanyNameErr(true);
      //   setCompanyNameErrTxt(translate('alphabeticCharacters', ''));
      // } else {
      //   setCompanyNameErr(false);
      // }

      if (String(companyName).trim()) {
        if (String(companyName).length < 3) {
          isValid = false;
          setCompanyNameErr(true);
          setCompanyNameErrTxt(translate('leastCharacters', ''));
        } else if (String(companyName).length > 100) {
          isValid = false;
          setCompanyNameErr(true);
          setCompanyNameErrTxt(translate('maximumCharacters', ''));
        } else if (!/^[A-Za-z\s]+$/.test(companyName)) {
          isValid = false;
          setCompanyNameErr(true);
          setCompanyNameErrTxt(translate('alphabeticCharacters', ''));
        } else {
          setCompanyNameErr(false);
        }
      } else {
        setCompanyNameErr(false);
      }

      if (!phoneNumber) {
        setPhoneNumberError(true);
        isValidstep = false;
      } else {
        setPhoneNumberError(false);
      }

      if (!email) {
        setEmailErr(true);
        setEmailErrTxt('Email is required');
        isValidstep = false;
      } else if (!validateEmailFormat(email)) {
        setEmailErr(true);
        setEmailErrTxt('Please enter a valid email address');
        isValidstep = false;
      } else {
        setEmailErr(false);
      }

      if (!location) {
        setLocationErr(true);
        isValidstep = false;
      } else {
        setLocationErr(false);
      }

      if (!selectedGender) {
        setGenderError(true);
        isValidstep = false;
      } else {
        setGenderError(false);
      }
      if (!selectedDate) {
        setSelectDateErr(true);
        isValidstep = false;
      } else {
        setSelectDateErr(false);
      }

      if (!isValidstep) {
        return; // If validation fails, do not proceed
      }
    }

    // Validate My Details Step (Step 2)
    else if (stepNumber === 3) {
      let isvalidstep4 = true;
      if (isEmpty(selectedTags)) {
        setSelectedTagErr({ err: true, txt: translate('skillRequired', '') });
        isvalidstep4 = false;
      } else {
        setSelectedTagErr({ err: false, txt: '' });
      }
      if (!isvalidstep4) {
        return; // If validation fails, do not proceed
      }
      const tags = [...selectedTags];
      const loc = location;
      if (!isEmpty(tags)) {
        await handleReviewUpdate('add_skills');
      }
      setSelectedTags(tags);
      setLocation(loc);
    } else if (stepNumber === 4) {
      let isvalidstep5 = true;
      console.log('step 4 ===>', selectedTags, isEmpty(selectedTags));
      if (isEmpty(selectedTags)) {
        setSelectedTagErr({ err: true, txt: translate('skillRequired', '') });
        isvalidstep5 = false;
      } else {
        setSelectedTagErr({ err: false, txt: '' });
      }
      if (!isvalidstep5) {
        return; // If validation fails, do not proceed
      }
      console.log(
        'step last upload_an_experience_license_certificate ===>',
        selectedTags,
      );
      const tags = [...selectedTags];
      const loc = location;
      if (cvFile && !isEmpty(preExperince)) {
        await handleReviewUpdate('upload_an_experience_license_certificate');
      }
      setSelectedTags(tags);
      setLocation(loc);
    }

    // If validation passes, proceed to the next step
    if (isValid) {
      setActiveStep(stepNumber);
    }
  };
  const SkipChange = async (stepNumber: number) => {
    setActiveStep(stepNumber);
  };

  // Initialize work experience
  // useEffect(() => {
  //   if (userData?.qualifications?.length > 0) {
  //     const workExperiences = userData.qualifications
  //       .filter((qual: Qualification) => qual.docType === 'workExperience')
  //       .map((exp: Qualification, index: number) => ({
  //         id: index,
  //         company: exp.company || '',
  //         designation: exp.designation || '',
  //         isCurrCompany: exp.isCurrCompany || 0,
  //         startDate: exp.startDate
  //           ? moment(exp.startDate, 'MM/DD/YYYY')
  //           : undefined,
  //         endDate: exp.endDate ? moment(exp.endDate, 'MM/DD/YYYY') : undefined,
  //         description: exp.description || '',
  //         docType: 'workExperience',
  //       }));

  //     setExperienceList(workExperiences);
  //   } else {
  //     setExperienceList([
  //       {
  //         id: Date.now(),
  //         company: '',
  //         designation: '',
  //         isCurrCompany: 0,
  //         startDate: undefined,
  //         endDate: undefined,
  //         description: '',
  //         docType: 'workExperience',
  //       },
  //     ]);
  //   }
  // }, [userData]);

  // Initialize certifications
  // useEffect(() => {
  //   if (userData?.qualifications?.length > 0) {
  //     const certifications = userData.qualifications
  //       .filter((qual: Qualification) => qual.docType === 'certifications')
  //       .map((cert: Qualification, index: number) => ({
  //         id: index,
  //         company: cert.company || '',
  //         fileName: cert.fileName || '',
  //         startDate: cert.startDate
  //           ? moment(cert.startDate, 'MM/DD/YYYY')
  //           : undefined,
  //         endDate: cert.endDate
  //           ? moment(cert.endDate, 'MM/DD/YYYY')
  //           : undefined,
  //         docType: 'certifications',
  //       }));

  //     setCertificationList(certifications);
  //   } else {
  //     setCertificationList([
  //       {
  //         id: Date.now(),
  //         company: '',
  //         startDate: undefined,
  //         endDate: undefined,
  //         fileName: '',
  //         docType: 'certifications',
  //       },
  //     ]);
  //   }
  // }, [userData]);

  // // Initialize experienceList from userProfileData if available
  // useEffect(() => {
  //   if (userProfileData?.workExperince?.length > 0) {
  //     const formattedExperiences = userProfileData.workExperince.map(
  //       (exp: any, index: number) => ({
  //         id: index, // Using index to keep a unique id (or use UUID)
  //         company: exp.company || '',
  //         designation: exp.designation || '',
  //         isCurrCompany: exp.isCurrCompany || 0,
  //         startDate: exp.startDate
  //           ? moment(exp?.startDate, 'MM/DD/YYYY')
  //           : undefined,
  //         endDate: exp.endDate ? moment(exp?.endDate, 'MM/DD/YYYY') : undefined,
  //         description: exp.description || '',
  //         docType: exp.docType || 'workExperience',
  //       }),
  //     );
  //     setExperienceList(formattedExperiences);
  //   } else {
  //     setExperienceList([
  //       {
  //         id: Date.now(),
  //         company: '',
  //         designation: '',
  //         isCurrCompany: '',
  //         startDate: undefined,
  //         endDate: undefined,
  //         description: '',
  //         docType: 'workExperience',
  //       },
  //     ]);
  //   }
  // }, [userProfileData]);

  // // Initialize experienceList from userProfileData if available
  // useEffect(() => {
  //   if (userProfileData?.certifications?.length > 0) {
  //     const formattedExperiences = userProfileData.certifications.map(
  //       (exp: any, index: number) => ({
  //         id: index, // Using index to keep a unique id (or use UUID)
  //         company: exp.company || '',
  //         fileName: exp.fileName || '',
  //         startDate: exp.startDate
  //           ? moment(exp?.startDate, 'MM/DD/YYYY')
  //           : undefined,
  //         endDate: exp.endDate ? moment(exp?.endDate, 'MM/DD/YYYY') : undefined,
  //         docType: exp.docType || 'certifications',
  //       }),
  //     );
  //     setCertificationList(formattedExperiences);
  //   } else {
  //     setCertificationList([
  //       {
  //         id: Date.now(),
  //         company: '',
  //         startDate: undefined,
  //         endDate: undefined,
  //         fileName: '',
  //         docType: 'certifications',
  //       },
  //     ]);
  //   }
  // }, [userProfileData]);

  // Populate state on load

  const gettingFlagCode = getCountryFlagCode(countryCode);

  useEffect(() => {
    if (userData || userProfileData) {
      // setAbout(userData?.about || userProfileData?.about || '');
      console.log('userData or userProfileData loaded:', {description: aiDescription?.description, about:userData?.about,  aboutabout:userProfileData?.about});
      setAiDescription((p: any) => ({ ...p, description: aiDescription?.description || userData?.about || userProfileData?.about || '' }));
      setPreCertificate(
        userData?.certifications ? userData?.certifications : [],
      );
      setPreExperince(userData?.workExperience ? userData?.workExperience : []);
      setCvFile(userData?.cv || null);
      setLicenseFiles(userData?.licenses || userProfileData?.licenses || []);
      setSelectedGender(userData?.gender || userProfileData?.gender);
      setName(userData?.firstName || userProfileData?.firstName || '');
      setLastName(userData?.lastName || userProfileData?.lastName || '');
      setIsAvailable(userData?.isAvailable ? userData?.isAvailable : false);
      setPhoneNumber(
        userData?.phoneNumber || userProfileData?.phoneNumber || '',
      );
      setCompanyName(userData?.company || userProfileData?.company || '');
      setEmail(
        userData?.email || userProfileData?.email || userData?.googleId || '',
      );
      setReferralCode(
        userData?.invitedReferralCode ||
          userProfileData?.invitedReferralCode ||
          '',
      );
      setLocation((p: any) => ({
        ...p,
        description: userData?.location || userProfileData?.location || '',
        lat:
          userData?.coordinates?.coordinates[1] ||
          userProfileData?.coordinates?.lat ||
          '',
        long:
          userData?.coordinates?.coordinates[0] ||
          userProfileData?.coordinates?.long ||
          '',
      }));
      setSelectedDate(
        userData?.birthDate ? new Date(userData?.birthDate) : undefined,
      );
      setCountryCode(
        userData?.countryCode?.replace('+', '') ||
          userProfileData?.countryCode?.replace('+', '') ||
          '1',
      );

      setFlagCode(
        userData?.flagCode
          ? userData?.flagCode
          : gettingFlagCode || gettingFlagCode,
      );
      setUploadLicene(userData?.licenses || []);
      setProfileImage(
        userData?.profilePhoto
          ? `${userData?.profilePhoto || ''}`
          : userProfileData?.profilePhoto || '',
      );

      console.log(
        'userData?.profilePhoto ==>',
        userData?.baseUrl,
        userData?.profilePhoto,
      );
      setSelectedTags(userData?.skills || []);
    }
  }, [userData]);

  useFocusEffect(
    React.useCallback(() => {
      if (isOwnProfileScreen) {
        console.log('useFocuss effect called');
        updateUserData(userData?.id);
      }
      return () => {};
    }, []),
  );

  //Remove File
  const deleteFile = async (type: string, index?: any) => {
    setLoader(index ? index : 'cv');

    // Set up file data based on the type
    const data =
      type === 'cv'
        ? { fileName: cvFile?.fileName || cvFile }
        : type === 'certificate'
          ? { fileName: certificateFile?.fileName || certificateFile }
          : { array: [licenseFiles[index]?.id] || licenseFiles[index] }; // Get the fileName of the selected license file

    try {
      const url = BaseSetting.endpoints.removeDocument;
      const resp = await getApiData({
        endpoint: url,
        method: 'POST',
        data: data,
      });

      if (resp?.status) {
        // Toast.show(resp?.message, Toast.BOTTOM);

        if (type === 'cv') {
          setCvFile(''); // Clear CV file
        } else if (type === 'licence' && index !== undefined) {
          // Remove the specific file from licenseFiles
          const updatedFiles = licenseFiles.filter((_, i) => i !== index);
          setLicenseFiles(updatedFiles);
          // Show an error if no license files are left
          if (updatedFiles.length === 0) {
            setLicenseError('Please upload at least one license file');
          } else {
            setLicenseError('');
          }
        }
      } else {
        console.log('🚀 ~ deleteFile ~ Failed to delete file');
        // Toast.show(resp?.message, Toast.BOTTOM);
      }
      setLoader(false);
    } catch (err) {
      setLoader(false);
    }
  };
  // for Uploading Resume....
  // async function handlepickMultiplefile(imag: any) {
  //   let imagData = {};

  //   imagData = {
  //     type: imag.type,
  //     name: imag.name,
  //     uri: imag.uri,
  //   };
  //   // setUploadLoader({pdf: false, image: false, multiDoc: true});

  //   const url = BaseSetting.endpoints.uploadDocument;
  //   const imgFile = {file: imagData};
  //   try {
  //     const res = await getApiData({
  //       endpoint: url,
  //       method: 'POST',
  //       data: imgFile,
  //       formData: true,
  //     });
  //     console.log('🚀 ~ handlepickMultiplefile ~ res:', res);
  //     if (res?.status) {
  //       Toast.show(res?.message, Toast.BOTTOM);
  //       if (!isEmpty(licenseFiles)) {
  //         setLicenseFiles([...licenseFiles, res?.data.fileName]);
  //       } else {
  //         setLicenseFiles([res?.data.fileName]);
  //       }

  //       // if (IOS) {
  //       //   ActionSheetRefIOS.current.close();
  //       // } else {
  //       //   ActionSheetRef.current.hide();
  //       // }
  //       // setUploadLoader({pdf: false, image: false, multiDoc: false});
  //     } else {
  //       // setUploadLoader({pdf: false, image: false, multiDoc: false});
  //     }
  //   } catch (err) {
  //     // setUploadLoader({pdf: false, image: false, multiDoc: false});
  //     Toast.show(err?.message || translate('err', ''), Toast.LONG);
  //   }
  // }

  const formattedDate = selectedDate ? formatDate(selectedDate) : ''; // Format the date

  // update user profile

  const handleSubmit = async (data: {isSkipped?: boolean}) => {
    setLoader(data?.isSkipped ? 'skip' : true);
    const newObj: NewObj = {
      firstName: name || undefined,
      lastName: lastname || undefined,
      email: email || undefined,
      phoneNumber: phoneNumber || undefined,
      birthDate: formattedDate || undefined,
      company: companyName || undefined,
      gender: selectedGender ? selectedGender.toLowerCase() : undefined,
      location: location?.description || '',
      countryCode: `+${countryCode}` || undefined,
      flagCode: flagCode,
      skillIds: !isEmpty(selectedTags) ? selectedTags : undefined,
      about: userData?.about, // about || '',
      coordinates: {
        lat: location?.lat || '',
        long: location?.long || '',
      },
    };
    console.log('newObj:', newObj, { about: userData?.about, description: aiDescription?.description,  aboutabout: userProfileData?.about });

    if (data?.isSkipped) {
      newObj.isSkipped = data?.isSkipped;
    } else {
      newObj.isProfileSet = true;
    }
    // else if (cvFile && !isEmpty(preExperince) && !isEmpty(selectedTags) && !isEmpty(preCertificate) && !isEmpty(licenseFiles)) {
    //   newObj.isProfileSet = true;
    // }
    if (referralCode) {
      newObj.invitedReferralCode = referralCode;
    }

    newObj.cv = (() => {
      if (typeof cvFile === 'string') {
        if (cvFile.includes('http')) {
          // Extract the file name from the URL
          return cvFile.split('/').pop();
        } else {
          // Return the string directly if it doesn't contain 'http'
          return cvFile;
        }
      } else if (cvFile && typeof cvFile === 'object') {
        // Return the fileName if cvFile is an object
        return cvFile.fileName;
      }
      return ''; // Fallback if cvFile is neither string nor valid object
    })();

    newObj.profilePhoto =
      profileImage && profileImage?.includes('http')
        ? profileImage?.split('/').pop()
        : profileImage || '';
    // newObj.profilePhoto = 'file-1738313014744.jpg';
    // const d = profileImage?.split('/').length;
    const updatedLicenseFiles = licenseFiles
      .map((file: any) => {
        if (typeof file === 'string') {
          // If the file is a URL, extract the file name
          return file.split('/').pop();
        } else if (file?.fileName) {
          // If the file is an object, use the fileName property
          return file.fileName;
        }
        return null;
      })
      .filter(Boolean); // Filter out any null values

    // Update the licenseFiles state with the extracted file names
    setLicenseFiles(updatedLicenseFiles);

    if (!isEmpty(updatedLicenseFiles) && isArray(updatedLicenseFiles)) {
      newObj.licenses = updatedLicenseFiles.map(file => {
        console.log('Current file:', file); // Log the current file for debugging

        if (typeof file === 'string') {
          // Case 1: When `licenseFiles` is an array of URLs (strings)
          if (file.includes('http')) {
            const fileName = file.split('/').pop();
            console.log('Extracted file name:', fileName);
            return fileName; // Extract file name from URL
          } else {
            console.log('Returning file as is:', file);
            return file; // Return as is
          }
        } else if (typeof file === 'string') {
          return file;
        } else if (typeof file === 'object' && file?.fileName) {
          // Case 2: When `licenseFiles` is an array of objects
          console.log('Returning fileName from object:', file.fileName);
          return file.fileName; // Extract fileName from the object
        } else {
          console.warn('Unhandled file format:', file);
          return null; // Return null if the format is unknown
        }
      });
    }

    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.updateUser,
        method: 'POST',
        data: newObj,
      });
      if (res?.status === true) {
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);

        // if (params?.redirection === 'back') {
        //   navigation.goBack();
        // } else {
        if (activeStep === 1) {
          SkipChange(2);
        } else if (activeStep === 2) {
          SkipChange(3);
        } else if (activeStep === 3) {
          SkipChange(4);
        } else if (activeStep === 4) {
          resetStackScreen('BottomTabsNavigator');
          // navigation.replace('BottomTabsNavigator');
        }
        // }
        // dispatch(authActions.setUserData() as any);

        if (
          !data?.isSkipped
          // &&
          // cvFile &&
          // !isEmpty(preExperince) &&
          // !isEmpty(selectedTags) &&
          // !isEmpty(preCertificate) &&
          // !isEmpty(licenseFiles)
        ) {
          handleReviewUpdate('complete_profile_setup', res?.data);
        }
        dispatch(authActions.setUserProfileData(newObj) as any);
      } else {
        dispatch(authActions.setUserProfileData(newObj) as any);
        // Toast.show(res?.message || translate('err', ''), Toast.BOTTOM);
        console.log('error', res);
      }
      setLoader(false);
    } catch (err) {
      console.log('check Error');
      setLoader(false);
      // Toast.show(translate('err', ''), Toast.BOTTOM);
    }
  };

  const handleReviewUpdate = async (slug: any, data: any = null) => {
    const { status, badgeInfo } = await updateReview(slug, userData.id);

    if (status && badgeInfo) {
      const d = !isEmpty(data) ? { ...data } : userData;
      const updatedUserData = {
        ...d, // Keep all other properties
        badgeInfo, // Update badgeInfo
      };

      if (data) {
        dispatch(authActions.setUserData(updatedUserData) as any);
      }
      console.log(
        'User data updated successfully with new badgeInfo:',
        badgeInfo,
      );
    }
  };

  const onRefresh = React.useCallback(() => {
    console.log('Refreshing triggered');
    setRefreshing(true);

    // Simulating data fetching or API call
    setTimeout(() => {
      console.log('Refreshing completed');
      setRefreshing(false);
    }, 2000); // Simulate 2 seconds of refreshing time
  }, [userData]);

  const renderComponent = () => {
    switch (activeStep) {
    case 1:
      return (
        <MyDetails
          onNext={() => goToStep(2)}
          selectedGender={selectedGender}
          setSelectedGender={setSelectedGender}
          name={name}
          setName={setName}
          firstNameErr={firstNameErr}
          setFirstNameErr={setFirstNameErr}
          firstNameErrTxt={firstNameErrTxt}
          setFirstNameErrTxt={setFirstNameErrTxt}
          lastNameErr={lastNameErr}
          setLastNameErr={setLastNameErr}
          lastNameErrTxt={lastNameErrTxt}
          setLastNameErrTxt={setLastNameErrTxt}
          lastname={lastname}
          setLastName={setLastName}
          phoneNumber={phoneNumber}
          setPhoneNumber={setPhoneNumber}
          phoneNumberError={phoneNumberError}
          setPhoneNumberError={setPhoneNumberError}
          phoneNumberErrorTxt={phoneNumberErrorTxt}
          setPhoneNumberErrorTxt={setPhoneNumberErrorTxt}
          companyName={companyName}
          setCompanyName={setCompanyName}
          companyNameErr={companyNameErr}
          params={params}
          setCompanyNameErr={setCompanyNameErr}
          companyNameErrTxt={companyNameErrTxt}
          setCompanyNameErrTxt={setCompanyNameErrTxt}
          email={email}
          setEmail={setEmail}
          genderErr={genderErr}
          setGenderError={setGenderError}
          emailErr={emailErr}
          setEmailErr={setEmailErr}
          genderErrTxt={genderErrTxt}
          setGenderErrTxt={setGenderErrTxt}
          location={location}
          setLocation={setLocation}
          locationErr={locationErr}
          setLocationErr={setLocationErr}
          locationErrTxt={locationErrTxt}
          setLocationErrTxt={setLocationErrTxt}
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
          emailErrTxt={emailErrTxt}
          setEmailErrTxt={setEmailErrTxt}
          setSelectDateErr={setSelectDateErr}
          selectDateErr={selectDateErr}
          setSelectDateErrTxt={setSelectDateErrTxt}
          selectDateErrTxt={selectDateErrTxt}
          setCountryCode={setCountryCode}
          setFlagCode={setFlagCode}
          flagCode={flagCode}
          countryCode={countryCode}
          setProfileImage={setProfileImage}
          profileImage={profileImage}
          setImageName={setImageName}
          setReferralCode={setReferralCode}
          referralCode={referralCode}
          cvFile={cvFile}
          licenseFiles={licenseFiles}
          setLicenseFiles={setLicenseFiles}
          setFormatedAddresss={setFormatedAddresss}
          formatedAddress={formatedAddress}
          gettingFlagCode={gettingFlagCode}
          selectedTags={selectedTags}
          // about={about}
          setDescription={setDescription}
          description={description}
          setAiDescription={setAiDescription}
          aiDescription={aiDescription}

          // imageName={imageName}
        />
      );
    case 2:
      return (
        <About
          onNext={() => goToStep(3)}
          setSelectedTagErr={setSelectedTagErr}
          selectedtagErr={selectedtagErr}
          setSelectedTags={setSelectedTags}
          selectedTags={selectedTags}
          handleSubmit={handleSubmit}
          // setAbout={setAbout}
          // about={about}
          loader={loader}
          name={name}
          lastname={lastname}
          phoneNumber={phoneNumber}
          companyName={companyName}
          email={email}
          profileImage={profileImage}
          flagCode={flagCode}
          selectedGender={selectedGender}
          location={location}
          referralCode={referralCode}
          countryCode={countryCode}
          cvFile={cvFile}
          licenseFiles={licenseFiles}
          setLicenseFiles={setLicenseFiles}
          formatedAddress={formatedAddress}
          startDate={startDate}
          endDate={endDate}
          setDescription={setDescription}
          description={description}
          setAiDescription={setAiDescription}
          aiDescription={aiDescription}
        />
      );
    case 3:
      return (
        <Upload
          onNext={() => goToStep(4)}
          navigation={navigation}
          setIsUploadValid={setIsUploadValid}
          setCvError={setCvError}
          setLicenseError={setLicenseError}
          licenseError={licenseError}
          cvError={cvError}
          cvFile={cvFile}
          setCvFile={setCvFile}
          setLicenseFiles={setLicenseFiles}
          licenseFiles={licenseFiles}
          deleteFile={deleteFile}
          // Passing all state fields
          company={company}
          setCompany={setCompany}
          companyErr={companyErr}
          setCompanyErr={setCompanyErr}
          designation={designation}
          setDesignation={setDesignation}
          designationErr={designationErr}
          setDesignationErr={setDesignationErr}
          working={working}
          setWorking={setWorking}
          workingErr={workingErr}
          setWorkingErr={setWorkingErr}
          isCurrentCompany={isCurrentCompany}
          setIsCurrentCompany={setIsCurrentCompany}
          jobProfile={jobProfile}
          setJobProfile={setJobProfile}
          jobProfileErr={jobProfileErr}
          setJobProfileErr={setJobProfileErr}
          workingSince={workingSince}
          setWorkingSince={setWorkingSince}
          workingTill={workingTill}
          setWorkingTill={setWorkingTill}
          workingSinceErr={workingSinceErr}
          setWorkingSinceErr={setWorkingSinceErr}
          workingTillErr={workingTillErr}
          setWorkingTillErr={setWorkingTillErr}
          setWorkExperince={setWorkExperince}
          workExperince={workExperince}
          setExperienceList={setExperienceList}
          experienceList={experienceList}
          setCertificateFile={setCertificateFile}
          certificateFile={certificateFile}
          setcertificateFileError={setcertificateFileError}
          certificateFileError={certificateFileError}
          setCompanyName={setCompanyName}
          companyName={companyName}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          startDate={startDate}
          endDate={endDate}
          setCompanyNameForCerti={setCompanyNameForCerti}
          companyNameForCerti={companyNameForCerti}
          setCertificationList={setCertificationList}
          certificationList={certificationList}
          setErrors={setErrors}
          errors={errors}
          setErrorShow={setErrorShow}
          errorShow={errorShow}
          setCertificateErrorShow={setCertificateErrorShow}
          certificateerrorShow={certificateerrorShow}
          setPreCertificate={setPreCertificate}
          preCertificate={preCertificate}
          setPreExperince={setPreExperince}
          preExperince={preExperince}
          name={name}
          lastname={lastname}
          phoneNumber={phoneNumber}
          email={email}
          profileImage={profileImage}
          flagCode={flagCode}
          selectedGender={selectedGender}
          location={location}
          referralCode={referralCode}
          countryCode={countryCode}
          // about={about}
          setUploadLicenseFile={setUploadLicenseFile}
          uploadLicenseFile={uploadLicenseFile}
          formatedAddress={formatedAddress}
          setAddData={setAddData}
          addData={addData}
          ActionSheetRef={ActionSheetRef}
          ActionSheetRefIOS={ActionSheetRefIOS}
          setUploadLicene={setUploadLicene}
          uploadLicense={uploadLicense}
          selectedTags={selectedTags}
          setDescription={setDescription}
          description={description}
          setAiDescription={setAiDescription}
          aiDescription={aiDescription}
        />
      );
    case 4:
      return (
        <ReviewComponant
          navigation={navigation}
          ReviewComponant
          onNext={() => goToStep(4)}
          handleSubmit={handleSubmit}
          selectedGender={selectedGender}
          name={name}
          lastname={lastname}
          phoneNumber={phoneNumber}
          companyName={companyName}
          params={params}
          location={location}
          countryCode={countryCode}
          profileImage={profileImage}
          imageName={imageName}
          email={email}
          aiDescription={aiDescription}
          selectedDate={selectedDate}
          setSelectedTagErr={setSelectedTagErr}
          selectedtagErr={selectedtagErr}
          setSelectedTags={setSelectedTags}
          selectedTags={selectedTags}
          // setAbout={setAbout}
          // about={about}
          loader={loader}
          setIsUploadValid={setIsUploadValid}
          setCvError={setCvError}
          setLicenseError={setLicenseError}
          licenseError={licenseError}
          cvError={cvError}
          cvFile={cvFile}
          // handleSubmit={handleSubmit}
          setCvFile={setCvFile}
          setLicenseFiles={setLicenseFiles}
          licenseFiles={licenseFiles}
          deleteFile={deleteFile}
          isAvailable={isAvailable}
          setIsAvailable={setIsAvailable}
          certificationList={certificationList}
          setCertificationList={setCertificationList}
          experienceList={experienceList}
          setExperienceList={setExperienceList}
          isOwnProfileScreen={isOwnProfileScreen}
          setPreCertificate={setPreCertificate}
          preCertificate={preCertificate}
          setPreExperince={setPreExperince}
          preExperince={preExperince}
          orderType={orderType}
          previousScreen={previousScreen}
          currentStep={currentStep}
        />
      );
    default:
      return null;
    }
  };

  return (
    <View style={styles?.container}>
      {/* <StatusBar
        backgroundColor={BaseColors.white}
        barStyle={'dark-content'}
      /> */}
      <Header
        leftIcon={isOwnProfileScreen ? 'logo' : 'back-arrow'}
        title={translate(isOwnProfileScreen ? 'profile' : 'profileSet')}
        rSkip={isOwnProfileScreen ? null : 'true'}
        onLeftPress={() => {
          if (redirection === 'back') {
            navigation.replace('ProfileScreen');
          }
          if (params?.redirection === 'back' || redirection === 'back') {
            navigation.goBack();
          } else {
            navigation.replace('BottomTabsNavigator');
          }
        }}
        rightIcons={
          isOwnProfileScreen
            ? [
              {
                icon: 'settings',
                onPress: () => {
                  navigation.navigate('settings');
                },
                wrapStyle: BaseStyles.notificationIconStyle,
              },
              {
                icon: 'edit',
                onPress: () => {
                  navigation.navigate('ProfileSetUp', {
                    redirection: 'back',
                    skipTrue: 'true',
                  });
                },
                // badge: totalMsgCount?.totalCount || false,
                // wrapStyle: styles?.editIconSty,
                type: 'vectorIcon',
              },
            ]
            : null
        }
        skippedLoader={loader === 'skip'}
        onRightPress={() => {
          handleSubmit({ isSkipped: true });
          // navigation.replace('BottomTabsNavigator');
        }}
      />
      <KeyboardAvoidingView
        enabled
        style={{ flex: 1 }}
        behavior={isIOS() ? 'padding' : ''}>

        {!isOwnProfileScreen ? (
          <View style={styles?.setUpViewSty}>
            <View style={[styles?.setUpSty]}>
              {/* Step Indicator */}
              <View
                style={[
                  styles.stepContainer,
                  {
                    justifyContent: 'space-between',
                  },
                ]}>
                {[
                  translate('myDetail', ''),
                  translate('about', ''),
                  translate('Upload'),
                  translate('Review', ''),
                ].map((label, index) => {
                  const stepNumber: number = index + 1;
                  const isActive = stepNumber === activeStep;
                  const isCompleted = stepNumber < activeStep;
                  return (
                    <TouchableOpacity
                      key={`${stepNumber}+${index}`}
                      onPress={() => goToStep(stepNumber)}
                      style={styles.stepItem}>
                      <View
                        style={[
                          styles.stepCircle,
                          isActive && styles.activeCircle,
                          isCompleted && styles.completedCircle,
                        ]}>
                        <Text
                          style={[
                            styles.stepTxtSty,

                            isActive && styles.activetxtSty,
                            isCompleted && styles.completedtxtSty,
                          ]}>
                          {stepNumber}
                        </Text>
                      </View>
                      <Text
                        style={[
                          styles.stepLabel,
                          isActive && styles.activeLabel,
                          isCompleted && styles.completedLabel,
                        ]}>
                        {label}
                      </Text>
                      {index < 3 && (
                        <View
                          style={[
                            styles.stepLine,
                            {
                              width: IOS
                                ? Dimensions.get('screen').width / 8
                                : Dimensions.get('screen').width / 8,
                            },
                          ]}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>
          </View>
        ) : null}
        {/* <ScrollView
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            refreshControl={
              activeStep === 4 ? (
                <RefreshControl
                  onRefresh={onRefresh}
                  refreshing={refreshing}
                  colors={[BaseColors.primary]}
                  tintColor={BaseColors.primary}
                />
              ) : (
                <></>
              )
            }> */}
        <View
          style={{
            ...styles.componentContainer,
            marginBottom: isOwnProfileScreen
              ? Dimensions.get('screen').height / 10
              : 0,
          }}>
          {renderComponent()}
        </View>
        {/* </ScrollView> */}
      </KeyboardAvoidingView>
    </View>
  );
}
