import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { CustomIcon } from '@config/LoadIcons'; // Adjust import as per your project structure
import { BaseColors } from '@config/theme'; // Adjust import as per your project structure
import { translate } from '@language/Translate'; // Adjust import as per your project structure
import TextInput from '@components/UI/TextInput';
import styles from './styles';
import BaseSetting from '@config/setting';
import { getApiData } from '@app/utils/apiHelper';
import Toast from 'react-native-simple-toast';
import { isArray, isEmpty } from '@app/utils/lodashFactions';
import { formatDate } from '@app/utils/CommonFunction';
import moment from 'moment';

interface AiComponantProps {
  about?: string;
  setAbout?: any;
  Review?: boolean;
  type?: string;
  selectedTags?: any;
  skillsOptions?: any;
  setSkillsOptions?: any;
  jobTitle?: any;
  location?: any;
  startDate?: any;
  endTime?: any;
  startTime?: any;
  endDate?: any;
  setDescription?: any;
  aiDescription?: any;
  setAiDescription?: any;
}

const AiComponant: React.FC<AiComponantProps> = ({
  // about,
  // setAbout,
  // Review,
  type,
  selectedTags,
  jobTitle,
  skillsOptions,
  setSkillsOptions,
  location,
  startDate,
  endTime,
  startTime,
  endDate,
  // setDescription,
  setAiDescription,
  aiDescription,
}) => {
  const formattedStartDate = startDate ? formatDate(startDate) : '';
  const formattedEndDate = endDate ? formatDate(endDate) : '';
  const formattedStartTime = startTime
    ? moment(startTime).format('hh:mm A')
    : undefined;
  const formattedEndTime = endTime
    ? moment(endTime).format('hh:mm A')
    : undefined;
  const [loader, setLoader] = useState(false);
  const getDescription = async (description: any) => {
    if (isEmpty(selectedTags)) {
      Toast.show(translate('selectSkills'), Toast.SHORT);
      return false;
    }

    const skills: any = [];
    const User_Type = type === 'user';

    isArray(selectedTags) &&
      selectedTags.map((s: any) => {
        skills.push(s.name);
      });
    const data = User_Type
      ? {
        skills: skills,
        type: type,
        description: description || '',
        location: location?.description || '',
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
      }
      : {
        skills: skills,
        jobTitle: jobTitle,
        type: type,
        description: aiDescription?.description || '',
        location: location?.description || '',
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        startTime: formattedStartTime,
        endTime: formattedEndTime,
      };

    try {
      setLoader(true);
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.getDescription,
        method: 'GET',
        data: data,
      });

      if (res?.status === true) {
        // Set the job applicant list
        setAiDescription((p: any) => ({
          ...p,
          showDescription: res?.data?.showDescription || '',
          jobDescription: res?.data?.jobDescription || '',
          description: res?.data?.showDescription || '',
        }));
        setLoader(false);
        // setAbout(res?.data?.jobDescription);
      } else {
        Toast.show(res?.message, Toast.BOTTOM);

        // Show error message for unauthorized action
        // setAbout(''); // Ensure the list is empty
      }
      setLoader(false);
    } catch (err) {
      Toast.show(res?.message, Toast.BOTTOM);

      // Handle API errors
      console.error('Error fetching applicants:', err);
      setLoader(false);

      // setAbout(''); // Clear the list to prevent crashes
    }
  };

  // Using useCallback to memoize the API call for fetching skills
  const getSkillList = useCallback(async () => {
    setSkillsOptions({ ...skillsOptions, loader: true });
    try {
      const res = await getApiData({
        endpoint: BaseSetting.endpoints.subSkillList,
        method: 'GET',
      });
      if (res.status) {
        await res.data.map((skl: any) => {
          skl.key = skl.id;
          skl.selectable = false;
          skl.childSkills.map((chl: any) => {
            chl.key = chl.id;
          });
        });
        setSkillsOptions({
          loader: false,
          data: res.data,
          suggestions: res?.suggestions,
        });
      }
    } catch (err) {}
  }, []);

  useEffect(() => {
    getSkillList();
  }, []);

  return (
    <View>
      <View style={styles.aboutViewSty}>
        {type === 'user' ? (
          <View style={styles.row}>
            <Text style={styles.selfText}>{translate('yourSelf', '')} </Text>
          </View>
        ) : (
          <View style={styles.aboutView}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={styles.aboutText}>
                {translate('Description', '')}
              </Text>
              {/* <Text style={styles.optionalTxt}>
                ({translate('Optional', '')})
              </Text> */}
            </View>
          </View>
        )}
        <View style={styles.iconWrapper}>
          {loader ? (
            <ActivityIndicator color={BaseColors.white} />
          ) : (
            <CustomIcon
              name="ChatGPT-icon"
              size={18}
              color={BaseColors.white}
              onPress={() => {
                getDescription('');
              }}
            />
          )}
        </View>
      </View>

      <View style={styles.briefView}>
        {type === 'job' ? (
          <Text style={styles.briefText}>{translate('describeJob', '')}</Text>
        ) : (
          <Text style={styles.briefText}>{translate('provideBrif', '')}</Text>
        )}
      </View>

      <View style={styles.marginTop}>
        <TextInput
          value={aiDescription?.description || aiDescription}
          onChange={(value: string) => setAiDescription((p: any) => ({ ...p, description: value.trimStart() }))}
          textArea={true}
          placeholderText="Type here"
          maxLength={1000}
          placeholderStyle={styles.placeholderStyle}
          iseditable={true}
          style={styles.textInput}
        />
        {/* Word Count */}
        <Text style={styles.briefText}>{aiDescription?.description?.length} / 1000</Text>
        {/* <View style={{alignItems: 'flex-end'}}>
          <CustomIcon
            name="Frame1"
            size={28}
            color={BaseColors.primary}
            onPress={() => {
              getDescription(about);
            }}
          />
        </View> */}
      </View>
    </View>
  );
};

export default AiComponant;
